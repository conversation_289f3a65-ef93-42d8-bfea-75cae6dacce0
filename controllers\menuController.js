const { default: mongoose } = require('mongoose');
const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper')
const Menu = require('../models/menuModel')
const AppError = require('../utils/appError')
const { uploadFile, getFileStream } = require('../utils/awsStorage');
const { MongoNotConnectedError } = require('mongodb');
const Kitchentable = require('../models/kitchentableModel');
const User = require('../models/userModel');
const ShoppingList = require('../models/shoppingListModel');
const ShoppingListItem = require('../models/shoppingListItemModel');
const ShoppingListItemContribution = require('../models/shoppingListItemContributionModel');
const Menuchild = require('../models/menuchildModel');

/////////////////////////////////////// ONE MENU /////////////////////////////////////////
// @POST /menu/one/:id
// empty Menü/Rezept/Reciept is created
exports.createOneMenu = catchAsync(async (req, res, next) => {
  helper.devConsole("createOneMenu in menuController")
  helper.devConsole(req.body)

  // Get userId from req.userId (set by previous middleware) or fallback to req.body.user_id
  const userId = req.userId || req.body.user_id;

  // check if all necessary data is available
  if(
      !userId || // Use the determined userId variable
      !req.body.menu || // Check if menu object exists
      !req.body.menu.name ||
      !req.body.menu.description ||
      !req.body.menu.menuchilds || // Expects an array (can be empty)
      !req.body.menu.users ||       // Expects an array (can be empty)
      !req.body.menu.imagelink     // Expects a string (can be space)
  ){
      // Log which specific field might be missing for easier debugging
      let missingField = !userId ? 'userId' : !req.body.menu ? 'menu object' : !req.body.menu.name ? 'menu.name' : !req.body.menu.description ? 'menu.description' : !req.body.menu.menuchilds ? 'menu.menuchilds' : !req.body.menu.users ? 'menu.users' : 'menu.imagelink';
      helper.devConsole(`Missing field in createOneMenu: ${missingField}`);
      return next(new AppError(`Not every data was given at menuController.createOneMenu (Missing: ${missingField})`, 500));
  }

  // form users array using the determined userId
  req.body.menu.users = [
    {
      // HARDCODED roleId - consider making this dynamic if needed
      "roleId" : "663f824f981b50392aab7714",
      "userId" : userId // Use the userId variable
    },
    {
      // HARDCODED roleId - consider making this dynamic if needed
      "roleId" : "663f65d043c4a70258760812",
      "userId" : userId // Use the userId variable
    }
  ]

  helper.devConsole("newMenu")
  //helper.devConsole(req.body.menu)
  // create menu
  const newMenu = await Menu.create(
    req.body.menu
  );

  //helper.devConsole(newMenu)
  req.body.menuchild.parentId = newMenu._id
  req.body.menu._id = newMenu._id

  // create answerobject, if needed
  req.body.answerobject = newMenu


  // next
  helper.devConsole("end createOneMenu in menuController")
  next()

});

// @GET /menu/one/:menuid
exports.getOneMenu = catchAsync(async (req, res, next) => {
  helper.devConsole("getOneMenu in menuController")
    if(
      !req.params.menuid
    ){
        next(new AppError('Not every data was given at menuController.getOneMenu', 500))
    }
    // find one menu and automaticly return
    const foundMenu = await Menu.findOne({_id: req.params.menuid})
    .populate({
        path: 'menuchilds.menuChildId',
        populate: [{
            path: 'ingredients.unit',
            model: 'Unit'
        }, {
            path: 'ingredients.name',
            model: 'Grocery'
        }]
    })
    .populate('users.roleId')
    .populate('users.userId');

    // only export array with isStandard = true (active menuchild)
    if (foundMenu) {
      //console.log(foundMenu.menuchilds)
      foundMenu.menuchilds = foundMenu.menuchilds.filter(child => child.menuChildId.isStandard === true);
    }

    // Send success response
    req.body.answerobject = foundMenu

    // next
    next()

});



// @GET /menu/one/:menuid/free
exports.getOneFreeAccessMenu = catchAsync(async (req, res, next) => {
  helper.devConsole("getOneFreeAccessMenu in menuController")
    if(
      !req.params.menuid
    ){
        next(new AppError('Not every data was given at menuController.getOneMenu', 500))
    }
    // find one menu and automaticly return
    const foundMenu = await Menu.findOne({_id: req.params.menuid})
    .populate({
        path: 'menuchilds.menuChildId',
        populate: [{
            path: 'ingredients.unit',
            model: 'Unit'
        }, {
            path: 'ingredients.name',
            model: 'Grocery'
        }]
    })
    .populate('users.roleId')
    .populate('users.userId');

    // If free Access on Menu is not set true, no auth callback
    if(!foundMenu.freeAccess){
      next(new AppError('This menu is not free available...', 500))
    }

    // only export array with isStandard = true (active menuchild)
    if (foundMenu) {
      //console.log(foundMenu.menuchilds)
      foundMenu.menuchilds = foundMenu.menuchilds.filter(child => child.menuChildId.isStandard === true);
    }

    // Send success response
    req.body.answerobject = foundMenu

    // next
    next()

});




// @ UPDATE
// /menu/one/:id
exports.patchOneMenu = catchAsync(async (req, res, next) => {
  helper.devConsole("patchOneMenu in menuController invoked");

  let menuIdToUpdate = req.params.menuid;
  if (req.body.menu && req.body.menu.id) {
    menuIdToUpdate = req.body.menu.id;
  } else if (req.body.menu && req.body.menu._id) {
    menuIdToUpdate = req.body.menu._id;
  }

  if (!req.body.menu || !menuIdToUpdate) {
    helper.devConsole("patchOneMenu: Missing menu data or menuId");
    return next(new AppError('Not every data was given at menuController.patchOneMenu (missing menu object or menuId)', 500));
  }

  // Clone the menu object from the request body to avoid modifying it directly
  const updateDataForMenu = { ...req.body.menu };

  helper.devConsole(`patchOneMenu: Updating menu ${menuIdToUpdate} with data:`, updateDataForMenu);

  // --- BEGIN BUGFIX B-2: Synchronize MenuChild.seatCount ---
  if (updateDataForMenu.menuchilds && Array.isArray(updateDataForMenu.menuchilds)) {
    // 🔧 KRITISCH: Warte kurz, damit MenuChild-Erstellung abgeschlossen ist
    await new Promise(resolve => setTimeout(resolve, 100));

    // Fetch the current state of the menu, populating its menuchild references
    // to access the actual MenuChild documents (isStandard, seatCount).
    const menuBeforeUpdate = await Menu.findById(menuIdToUpdate).populate({
        path: 'menuchilds.menuChildId', // Populate the MenuChild document referenced by menuChildId
        model: 'MenuChild'         // Explicitly specify the model for clarity
    });

    if (!menuBeforeUpdate) {
      helper.devConsole(`patchOneMenu: Menu with ID ${menuIdToUpdate} not found for update.`);
      return next(new AppError('Menu not found for update', 404));
    }

    // Iterate through the menuchild entries provided in the update request's payload
    for (const incomingMenuChildRef of updateDataForMenu.menuchilds) {
      // Ensure the incoming reference has an ID (likely a string from frontend) and numberOfPersons
      if (incomingMenuChildRef.menuChildId && incomingMenuChildRef.numberOfPersons !== undefined) {
        const incomingMenuChildIdStr = incomingMenuChildRef.menuChildId.toString();

        // Find the corresponding menuchild entry from the database version of the menu
        // This dbMenuChildEntry contains the populated MenuChild document itself
        const dbMenuChildEntry = menuBeforeUpdate.menuchilds.find(
          mc => mc.menuChildId && mc.menuChildId._id.toString() === incomingMenuChildIdStr
        );

        if (dbMenuChildEntry && dbMenuChildEntry.menuChildId) {
            const actualMenuChildDocInDB = dbMenuChildEntry.menuChildId; // This is the populated MenuChild document

            // Check if this MenuChild is the standard one AND its seatCount in the DB
            // differs from the numberOfPersons sent in the current update request for this child.
            if (actualMenuChildDocInDB.isStandard === true &&
                actualMenuChildDocInDB.seatCount !== incomingMenuChildRef.numberOfPersons) {

                helper.devConsole(`patchOneMenu: Standard MenuChild ${actualMenuChildDocInDB._id} seatCount is changing from ${actualMenuChildDocInDB.seatCount} to ${incomingMenuChildRef.numberOfPersons}`);

                // Perform a direct update on the MenuChild document to change its seatCount
                await Menuchild.updateOne(
                    { _id: actualMenuChildDocInDB._id },
                    { $set: { seatCount: incomingMenuChildRef.numberOfPersons } }
                );
                helper.devConsole(`patchOneMenu: MenuChild ${actualMenuChildDocInDB._id} seatCount updated to ${incomingMenuChildRef.numberOfPersons}`);
            }
        } else {
            // 🔧 KRITISCH: Verbesserte Fehlerbehandlung für AI-Generator
            helper.devConsole(`patchOneMenu: Did not find DB entry for incoming menuchild ref ID: ${incomingMenuChildIdStr} - this is normal for newly created recipes`);

            // Versuche direkt das MenuChild zu finden (für frisch erstellte Rezepte)
            try {
              const directMenuChild = await Menuchild.findById(incomingMenuChildIdStr);
              if (directMenuChild) {
                helper.devConsole(`patchOneMenu: Found MenuChild directly: ${directMenuChild._id}, isStandard: ${directMenuChild.isStandard}`);
              } else {
                helper.devConsole(`patchOneMenu: MenuChild ${incomingMenuChildIdStr} not found in database yet - timing issue`);
              }
            } catch (findError) {
              helper.devConsole(`patchOneMenu: Error finding MenuChild directly: ${findError.message}`);
            }
        }
      } else {
        helper.devConsole("patchOneMenu: Skipping an incoming menuchild ref, missing ID or numberOfPersons", incomingMenuChildRef);
      }
    }
  }
  // --- END BUGFIX B-2 ---

  // Now, update the main Menu document with the data from updateDataForMenu
  // This will save changes to Menu.name, Menu.description, Menu.menuchilds array (including its numberOfPersons fields), etc.
  await Menu.updateOne({ _id: menuIdToUpdate }, { $set: updateDataForMenu });
  helper.devConsole(`patchOneMenu: Menu document ${menuIdToUpdate} updated.`);

  // Fetch the updated menu to return the true, populated state from DB as the response
  // This ensures the response reflects all changes and is consistent with getOneMenu.
  const finalUpdatedMenu = await Menu.findById(menuIdToUpdate)
    .populate({
        path: 'menuchilds.menuChildId',
        populate: [{ path: 'ingredients.unit', model: 'Unit' }, { path: 'ingredients.name', model: 'Grocery' }]
    })
    .populate('users.roleId')
    .populate('users.userId');

  // Filter the menuchilds in the response to only include the standard one(s), similar to getOneMenu
  if (finalUpdatedMenu && finalUpdatedMenu.menuchilds) {
    finalUpdatedMenu.menuchilds = finalUpdatedMenu.menuchilds.filter(childRef => childRef.menuChildId && childRef.menuChildId.isStandard === true);
  }

  req.body.answerobject = finalUpdatedMenu;
  helper.devConsole("patchOneMenu: Responding with updated and filtered menu.");
  next();
});


// @ DELETE
// /one/:menuid/delete/relateduser
exports.deleteRelatedUserById = catchAsync(async (req, res, next) => {
  helper.devConsole("deleteRelatedUserById in menuController")
  //helper.devConsole(req.body)
  helper.devConsole(req.body.user_id)
  //helper.devConsole(req.params.menuid)
  if(
      !req.body.user_id ||
      !req.params.menuid
  ){
      next(new AppError('Not every data was given at menuController.patchOneMenu', 500))
  }
  //helper.devConsole(req.body.menu)
  //helper.devConsole(req.params.menuid)

  // find and delete users from array

  // model data
  const deletedObjects = await Menu.updateMany({ _id: req.params.menuid }, { $pull: { users: { userId: req.body.user_id } } });

  //next
  req.body.answerobject = deletedObjects
  next()

});



/////////////////////////////////////// //////////// /////////////////////////////////////////

////////////////////////////////////// MENUS BY USER ID /////////////////////////////////////
// Function to escape regex special characters
function escapeRegex(text) {
    if (!text) return '';
    return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
}

// @desc      Get all Menus for a specific user, potentially filtered by kitchentable and search string
// @route     GET /api/v1/menu/complete/allbyuserid/:userid/kitchentableid/:kitchentableid
// @route     GET /api/v1/menu/complete/allbyuserid/:userid  (without kitchentable filter)
exports.getAllMenusByUserid = catchAsync(async (req, res, next) => {
  helper.devConsole(`[getAllMenusByUserid] Start. UserID: ${req.params.userid}, KitchentableID: ${req.params.kitchentableid}`);

  // 1. Extract and Validate Parameters
  const userId = req.params.userid;
  const kitchentableId = req.params.kitchentableid;
  const scope = req.query.scope;
  const searchstring = req.query.searchstring;
  const sortQuery = req.query.sort || '-createdAt';
  const page = parseInt(req.query.page, 10) || 0;
  const limit = parseInt(req.query.limit, 10) || 9;
  const skip = page * limit;
  const filterOnShoppingList = req.query.filterOnShoppingList === 'true';

  if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
      return next(new AppError('Invalid User ID provided', 400));
  }

  // --- Determine Base Query Filter based on Scope and Kitchentable ---
  let baseQueryFilter = {}; // Start with an empty filter
  let useKitchentableFilter = false;
  const requestingUserId = req.user._id; // Get the ID of the *authenticated* user

  if (kitchentableId) {
      if (!mongoose.Types.ObjectId.isValid(kitchentableId)) {
          helper.devConsole(`[getAllMenusByUserid] Invalid Kitchentable ID format: ${kitchentableId}. Falling back to user scope of requesting user.`);
          baseQueryFilter = { 'users.userId': requestingUserId }; // Filter by the user making the request
      } else if (scope === 'kitchentable') {
          try {
              // Find the kitchentable and populate member IDs for filtering
              const kt = await Kitchentable.findById(kitchentableId).select('members.userId');
              if (!kt) {
                   helper.devConsole(`[getAllMenusByUserid] Kitchentable ${kitchentableId} not found. Falling back to user scope of requesting user.`);
                   baseQueryFilter = { 'users.userId': requestingUserId };
              } else {
                   // Check if the *requesting* user is actually a member
                   const isMember = kt.members.some(member => member.userId.equals(requestingUserId));
                   if (!isMember) {
                       // If requesting user is NOT a member, they shouldn't see anything from this KT
                       // Throwing an error handled by verify middleware usually, but double-check here
                       helper.devConsole(`[getAllMenusByUserid] Authorization Error: Requesting user ${requestingUserId} not a member of KT ${kitchentableId}.`);
                       return next(new AppError('Not authorized for this kitchentable', 403));
                   }

                   helper.devConsole("[getAllMenusByUserid] Scope: kitchentable. Filtering by KT members.");
                   // Filter by Menus where AT LEAST ONE user in the Menu's users array
                   // is also a member of the requested Kitchentable.
                   const memberIds = kt.members.map(m => m.userId);
                   baseQueryFilter = { 'users.userId': { $in: memberIds } };
                   useKitchentableFilter = true; // Indicate successful KT validation and filtering
              }
          } catch (error) {
              helper.devConsole(`[getAllMenusByUserid] Error validating Kitchentable ${kitchentableId}: ${error.message}. Falling back to user scope of requesting user.`);
              baseQueryFilter = { 'users.userId': requestingUserId };
          }
      } else {
          // Kitchentable ID provided, but scope is not 'kitchentable'
          helper.devConsole(`[getAllMenusByUserid] Kitchentable ID provided but scope is not 'kitchentable' (${scope}). Applying filter for specific user from path: ${userId}.`);
          baseQueryFilter = { 'users.userId': new mongoose.Types.ObjectId(userId) }; // Filter by the user specified in the path
      }
  } else {
      // No Kitchentable ID provided - filter by the specific user from the path
      helper.devConsole("[getAllMenusByUserid] No Kitchentable ID provided. Applying filter for specific user from path: ${userId}.");
      baseQueryFilter = { 'users.userId': new mongoose.Types.ObjectId(userId) };
  }

  // --- Fetch IDs from Current Shopping List (active or completed) ---
  let recipeIdsOnActiveList = new Set();
  let objectIdsOnActiveList = [];

  // ERWEITERT: Shopping List Filter auch für "Meine Rezepte" unterstützen
  let kitchentableIdForShoppingList = null;
  if (useKitchentableFilter && kitchentableId) {
      kitchentableIdForShoppingList = kitchentableId;
  } else if (filterOnShoppingList && scope === 'my_reciepts') {
      // Für "Meine Rezepte": Finde das Standard-Küchentisch des Users
      try {
          const user = await User.findById(requestingUserId).select('defaultKitchentable');
          if (user && user.defaultKitchentable) {
              kitchentableIdForShoppingList = user.defaultKitchentable;
              helper.devConsole(`[getAllMenusByUserid] Using user's default kitchentable ${kitchentableIdForShoppingList} for shopping list filter in 'my_reciepts' scope.`);
          } else {
              helper.devConsole(`[getAllMenusByUserid] No default kitchentable found for user ${requestingUserId} in 'my_reciepts' scope.`);
          }
      } catch (error) {
          helper.devConsole(`[getAllMenusByUserid] Error fetching user's default kitchentable: ${error.message}`);
      }
  }

  if (kitchentableIdForShoppingList) {
      try {
          // First try to find an active list, then fall back to the most recent list (including completed)
          let currentList = await ShoppingList.findOne({
              kitchentable_id: new mongoose.Types.ObjectId(kitchentableIdForShoppingList),
              is_active: true
          }).select('_id');

          // If no active list found, get the most recent list (including completed ones)
          if (!currentList) {
              currentList = await ShoppingList.findOne({
                  kitchentable_id: new mongoose.Types.ObjectId(kitchentableIdForShoppingList)
              }).sort({ updatedAt: -1 }).select('_id');
              helper.devConsole(`[getAllMenusByUserid] No active list found, using most recent list: ${currentList?._id}`);
          } else {
              helper.devConsole(`[getAllMenusByUserid] Using active list: ${currentList._id}`);
          }

          if (currentList) {
              const items = await ShoppingListItem.find({
                  shopping_list_id: currentList._id,
                  is_custom: false,
                  recipeId: { $exists: true }
              }).distinct('recipeId');

              helper.devConsole(`[getAllMenusByUserid] Raw items from distinct('recipeId'):`, items);
              items.forEach(idValue => {
                  if (idValue !== null && idValue !== undefined) {
                      try {
                          recipeIdsOnActiveList.add(idValue.toString());
                      } catch (e) {
                          helper.devConsole(`[getAllMenusByUserid] Error calling toString() on recipeId value: ${idValue}. Error: ${e.message}`);
                      }
                  } else {
                      helper.devConsole(`[getAllMenusByUserid] Found null or undefined recipeId in ShoppingListItems for list ${currentList._id}. Skipping.`);
                  }
              });

              helper.devConsole(`[getAllMenusByUserid] Populated recipeIdsOnActiveList (Set of strings):`, recipeIdsOnActiveList);

              if (recipeIdsOnActiveList.size > 0) {
                 objectIdsOnActiveList = Array.from(recipeIdsOnActiveList).map(idStr => {
                    try {
                        return new mongoose.Types.ObjectId(idStr);
                    } catch (e) {
                        helper.devConsole(`[getAllMenusByUserid] Error converting recipeId string '${idStr}' to ObjectId. Error: ${e.message}`);
                        return null; // Or handle error appropriately
                    }
                 }).filter(id => id !== null); // Filter out any nulls from conversion errors
              }
              helper.devConsole(`[getAllMenusByUserid] Converted objectIdsOnActiveList (Array of ObjectIds):`, objectIdsOnActiveList);

          } else {
              helper.devConsole(`[getAllMenusByUserid] No shopping list found for KT ${kitchentableIdForShoppingList}.`);
          }
      } catch (error) {
          helper.devConsole(`[getAllMenusByUserid] WARN: Outer catch block. Error fetching active shopping list recipe IDs: ${error.message}. Proceeding without isOnActiveShoppingList=true.`);
          recipeIdsOnActiveList = new Set();
          objectIdsOnActiveList = [];
      }
  }

  // --- Prepare Sort Options ---
  const sortOptions = {};
  if (sortQuery) {
      const sortBy = sortQuery.split(',').join(' ');
      sortOptions[sortBy.startsWith('-') ? sortBy.substring(1) : sortBy] = sortBy.startsWith('-') ? -1 : 1;
  }

  // --- Execute Query (ALWAYS use Aggregation now) ---
  let foundMenus = [];
  let totalMenus = 0;
  const safeSearchTerm = escapeRegex(searchstring);

  try {
      // --- ALWAYS USE AGGREGATION ---
      helper.devConsole(`[getAllMenusByUserid] Using AGGREGATION.`);
      const pipeline = [];

      // 1. Initial Match (User/Kitchentable)
      pipeline.push({ $match: baseQueryFilter });

       // --- NEW: Conditionally add $match for filterOnShoppingList ---
       if (filterOnShoppingList) {
           if (objectIdsOnActiveList.length > 0) {
               helper.devConsole(`[getAllMenusByUserid] Applying filter: Only menus on active shopping list.`);
               pipeline.push({
                   $match: {
                       _id: { $in: objectIdsOnActiveList }
                   }
               });
           } else {
               // Edge case: Filter is requested, but list is empty or failed to load
               helper.devConsole(`[getAllMenusByUserid] WARN: filterOnShoppingList=true requested, but no items found on active list. Returning empty results.`);
               // Add a match that's always false to return empty results efficiently
               pipeline.push({ $match: { _id: new mongoose.Types.ObjectId('000000000000000000000000') } }); // Match impossible ID
           }
       }
       // --- END NEW ---

       // --- Conditionally add $match for search string ---
      if (safeSearchTerm) {
          helper.devConsole(`[getAllMenusByUserid] Adding search term filter: "${safeSearchTerm}"`);
          pipeline.push({
              $match: {
                  $or: [
                      // ONLY search in Menu name and description
                      { name: { $regex: safeSearchTerm, $options: 'i' } },
                      { description: { $regex: safeSearchTerm, $options: 'i' } }
                  ]
              }
          });
            // NOTE: If using $text index later, this $match stage changes to:
            // pipeline.push({ $match: { $text: { $search: searchstring } } });
            // And the initial $match needs to come *after* the $text match.
      }

      // 2. Lookup MenuChilds (Only needed if filtering/accessing child data)
      pipeline.push({
          $lookup: {
              from: 'menuchildren',
              localField: 'menuchilds.menuChildId',
              foreignField: '_id',
              as: 'populatedMenuChilds'
          }
      });

        // --- Add cookingTime filter (if provided) ---
       // This needs to filter the *parent* Menu based on child properties.
       // We filter Menus where at least one standard MenuChild meets the criteria.
       if (req.query.cookingTime && req.query.cookingTime.lte) {
           const maxCookingTime = parseInt(req.query.cookingTime.lte, 10);
           if (!isNaN(maxCookingTime)) {
               helper.devConsole(`[getAllMenusByUserid] Adding cookingTime filter: <= ${maxCookingTime}`);
               pipeline.push({
                   $match: {
                       'populatedMenuChilds': {
                           $elemMatch: { // Find menus where at least one child matches
                               // isStandard: true, // Optional: Only consider standard menu child? Add if needed.
                               cookingTime: { $lte: maxCookingTime }
                           }
                       }
                   }
               });
           }
       }

      // 3. Lookup Groceries (Only needed if search requires it - keep for now or optimize later)
      // If search ONLY targets Menu name/desc, this might be removable if not searching ingredients
      // pipeline.push({
      //     $lookup: {
      //         from: 'groceries',
      //         localField: 'populatedMenuChilds.ingredients.name',
      //         foreignField: '_id',
      //         as: 'populatedGroceries' // This name is confusing if only used for search
      //     }
      // });

      // 4. Facet for Pagination and Count (applied AFTER filtering)
      const facetStage = {
          $facet: {
              metadata: [{ $count: "totalMenus" }],
              data: [] // Sorting, Skip, Limit will be added here
          }
      };

      // Add sorting to the data pipeline within facet
      if (Object.keys(sortOptions).length > 0) {
          facetStage.$facet.data.push({ $sort: sortOptions });
      }
      // Add skip and limit
      facetStage.$facet.data.push({ $skip: skip });
      facetStage.$facet.data.push({ $limit: limit });

       // Add lookups needed for the final populated data *within* the data pipeline of facet
       facetStage.$facet.data.push(
           // This $lookup populates ALL menuchildren associated with the current menu document
           // based on the IDs in its 'menuchilds' array (from the Menu model).
           { $lookup: { from: 'menuchildren', localField: 'menuchilds.menuChildId', foreignField: '_id', as: 'populatedMenuChildsFinal' } }
       );

       // --- CORRECTLY IDENTIFY AND USE THE STANDARD MENUCHILD ---
       facetStage.$facet.data.push({
           $addFields: {
               // Filter populatedMenuChildsFinal to find those where isStandard is true.
               // $filter returns an array. We typically expect one standard child per menu.
               standardMenuChildArray: {
                   $filter: {
                       input: "$populatedMenuChildsFinal",
                       as: "child",
                       cond: { $eq: [ "$$child.isStandard", true ] }
                   }
               }
           }
       });
       facetStage.$facet.data.push({
           $addFields: {
               // Get the first element from the filtered array (this should be the standard child).
               // If no standard child is found (e.g. data issue), standardMenuChild will be null.
               standardMenuChild: { $arrayElemAt: [ "$standardMenuChildArray", 0 ] }
           }
       });
       facetStage.$facet.data.push({
           $addFields: {
               // Extract cookingTime and use seatCount for numberOfPersons FROM THE STANDARD CHILD.
               // Use $ifNull for robustness, providing null if standardMenuChild is not found.
               cookingTime: { $ifNull: [ "$standardMenuChild.cookingTime", null ] },
               numberOfPersons: { $ifNull: [ "$standardMenuChild.seatCount", null ] },
               // Optional: include the ID of the standard menuchild if needed by the frontend.
               standardMenuChildId: { $ifNull: [ "$standardMenuChild._id", null ] }
           }
       });
        // --- END CORRECTED FIELD ADDITION ---

       // Subsequent lookups (units, groceries, etc.) should now use 'standardMenuChild' fields.
       facetStage.$facet.data.push(
           { $lookup: { from: 'units', localField: 'standardMenuChild.ingredients.unit', foreignField: '_id', as: 'units_pop' } },
           { $lookup: { from: 'groceries', localField: 'standardMenuChild.ingredients.name', foreignField: '_id', as: 'groceries_pop' } },
           { $lookup: { from: 'roles', localField: 'users.roleId', foreignField: '_id', as: 'roles_pop' } },
           { $lookup: { from: 'users', localField: 'users.userId', foreignField: '_id', as: 'users_pop', pipeline: [{ $project: { firstName: 1, lastName: 1, _id: 1 } }] } }
       );

        // Final $project stage to shape the output for each menu item.
        facetStage.$facet.data.push({
            $project: {
                // --- Fields to include in the final output for each menu ---
                "name": 1,
                "description": 1,
                "users": 1,             // Original users array from Menu
                "imagelink": 1,
                "freeAccess": 1,
                "createdAt": 1,
                "updatedAt": 1,         // Good to keep
                // "menuchilds": 1,    // Original menuchilds array from Menu (optional, uncomment if needed by frontend)

                // Fields derived from the standardMenuChild
                "cookingTime": 1,
                "numberOfPersons": 1,   // This should now be correct
                "standardMenuChildId": 1,// ID of the standard menuchild (optional, if added in previous stage)

                // _id is included by default. If you want to explicitly exclude it, you can add "_id": 0.
                // For most list views, _id is useful, so it's often kept.

                // --- Implicitly Excluded Fields ---
                // Temporary fields like populatedMenuChildsFinal, standardMenuChildArray, standardMenuChild
                // are automatically excluded because they are not listed here with a '1'.

                // Populated fields like units_pop, groceries_pop, roles_pop, users_pop are also
                // implicitly excluded if not listed. If any of these ARE needed in the final
                // menu object sent to the client, they should be listed here with a '1'.
                // For example, if you transformed users_pop into a simpler structure and want to include it:
                // "processedUsers": 1, // (assuming an earlier stage created this field)
            }
        });

      pipeline.push(facetStage);

      console.log('[getAllMenusByUserid] Aggregation Pipeline:', JSON.stringify(pipeline, null, 2));

      const aggregationResult = await Menu.aggregate(pipeline);

      if (aggregationResult && aggregationResult[0]) {
          foundMenus = aggregationResult[0].data;
          totalMenus = aggregationResult[0].metadata.length > 0 ? aggregationResult[0].metadata[0].totalMenus : 0;
      } else {
          foundMenus = [];
          totalMenus = 0;
      }
      helper.devConsole(`[getAllMenusByUserid] Aggregation finished. Found ${foundMenus.length} menus on page ${page}. Total matching: ${totalMenus}`);

      // --- REMOVED THE OLD FIND PATH ---

  } catch (dbError) {
      console.error(`[getAllMenusByUserid] DATABASE ERROR: ${dbError}`);
      return next(new AppError('Error retrieving menus from database', 500));
  }

  // --- Process Results and Add Flags ---
  const menusWithFlags = foundMenus.map(menu => {
      const menuObject = menu; // Already plain object from aggregate

      // Add the isOnActiveShoppingList flag
      menuObject.isOnActiveShoppingList = recipeIdsOnActiveList.has(menuObject._id.toString());

      // --- FIX: Manually construct image URL (since post hook is bypassed) ---
      if (menuObject.imagelink) {
          const s3BasePath = process.env.S3_PATH || ''; // Get base path from env
          menuObject.imagelink = s3BasePath + menuObject.imagelink;
      } else {
          menuObject.imagelink = ""; // Ensure it's an empty string if null/undefined
      }
      // --- END FIX ---

       // REMOVED logic for filtering menuchilds and setting numberOfPersons here
       // This is now handled within the aggregation pipeline ($addFields)

      return menuObject;
  });

  // --- Send Response ---
  req.body.answerobject = {
    menus: menusWithFlags,
    totalPages: Math.ceil(totalMenus / limit),
    currentPage: page
  };
  next();
});
///////////////////////////////////////////////////////////////////////////////////////


////////////////////////////////////// MENUS BY FREE ACCESS /////////////////////////////////////

// @GET /menu/complete/all/freeaccess
exports.getAllMenusOnFreeAccess = catchAsync(async (req, res, next) => {
  // def vars
  let foundMenus = ''
  if(
    !req.params.searchstring
  ){
    // no searchstring found > search all first 5 menus
    foundMenus = await Menu.find({
      freeAccess: true
    })
    .populate({
        path: 'menuchilds.menuChildId',
        populate: [{
            path: 'ingredients.unit',
            model: 'Unit'
        }, {
            path: 'ingredients.name',
            model: 'Grocery'
        }]
    })
    .populate('users.roleId')
    .populate('users.userId')
    .limit(12);

  } else {
    // find one menu and automaticly return
    function escapeRegex(text) {
      return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
    }

    const safeSearchTerm = escapeRegex(req.params.searchstring);

    foundMenus = await Menu.find({
      name: { $regex: safeSearchTerm, $options: 'i' },
      freeAccess: true
    })
    .populate({
        path: 'menuchilds.menuChildId',
        populate: [{
            path: 'ingredients.unit',
            model: 'Unit'
        }, {
            path: 'ingredients.name',
            model: 'Grocery'
        }]
    })
    .populate('users.roleId')
    .populate('users.userId')
    .limit(12);
  }

  helper.devConsole(foundMenus)
  // Send success response
  req.body.answerobject = foundMenus

  // next
  next()

});

/////////////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////  menuChecker //////////////////////////////
// serverside to check if menu was created completly
// serverside menuController.menuChecker
exports.menuChecker = catchAsync(async (req, res, next) => {
  helper.devConsole("menuChecker in menuController")
  helper.devConsole(req.body.menu._id)
  helper.devConsole(req.body)

  // no searchstring found > search all first 5 menus
  let foundMenu = await Menu.findOne({
    _id: req.body.menu._id
  })

  if(foundMenu.menuchilds.length > 0){
  // Send success response
  req.body.answerobject = foundMenu
  // next
  next()

  } else {
  // Delete error object in DB
  await Menu.findByIdAndDelete(req.body.menu._id);
  // Send bad news to user
  req.body.answerobject = "Es ist ein Fehler aufgetreten. Bitte versuche es erneut."
  //next
  next()
  }


});
////////////////////////////////////////////////////////////////////////////////////////

// getOneMenueByPersons
// @GET /menu/one/:id/:persons
/*
exports.checkAndAddOneMenueByPersons = catchAsync(async (req, res, next) => {
  helper.devConsole(req.params.id)
  helper.devConsole(req.params.persons)
  ////////// SETTINGS //////////
  //
  /////////////////////////////
  try{
    // find one menu and with correct number of persons and automaticly return
    const menue = await Menu.findOne({_id: req.params.id}, {plannedSeats: req.params.persons})
    //.populate('zutaten.unit')
    // Send success response
    res.status(201).json({
      status: 'success',
      data: {
        "menue" : "menu"
      }
    });

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});
*/


//
// POST @ /menu/images/upload
exports.uploadOneImage = catchAsync(async (req, res, next) => {

    //helper.devConsole(req.body.image)
    //helper.devConsole(req.file)

    // upload image
    const resp = await uploadFile(req.file)

    helper.devConsole("imagelink")
    helper.devConsole(resp)

    // send object id (req.body.menuid) for next() -> patchOne
    req.params.menuid = req.body.menuid
    req.body.menu = { "imagelink": resp.key }

    helper.devConsole(req.params.menuid)
    helper.devConsole(req.body.menu)

    // next()
    next();

});

//
exports.downloadOneImage = catchAsync(async (req, res, next) => {

    // load file stream from aws bucket
    const readStream = getFileStream(req.params.id)
    readStream.pipe(res)

});


// @GET /menu/one/byIngridient/:ingridient
exports.getOneMenuByIngridient = catchAsync(async (req, res, next) => {

    // find one menu and automaticly return
    helper.devConsole(req.params.ingridient)
    let menue = await Menu.findOne({
      $or: [
        { "zutaten.name": { $regex: req.params.ingridient, $options: "i" }},
        { "zubereitung.content": { $regex: req.params.ingridient, $options: "i" }}
      ]
    })


    helper.devConsole(menue)

    if(menue == null){
      menue.data.data.imagelink = ''
    }

    helper.devConsole(menue)


});

// --- Internal helper function to fetch and populate a random free menu ---
async function _fetchRandomFreeMenuData() {
  // 1. Find one random menu document using aggregation
  const randomMenuArr = await Menu.aggregate([
    { $match: { freeAccess: true } }, // Filter for free access
    { $sample: { size: 1 } }          // Randomly select one document
  ]);

  // 2. Check if a menu was found
  if (!randomMenuArr || randomMenuArr.length === 0) {
    // Return null or throw an error that can be caught by the caller
    // Returning null might be easier for the cron job to handle
    return null;
  }

  const randomMenuId = randomMenuArr[0]._id;
  helper.devConsole(`[_fetchRandomFreeMenuData] Found random free menu ID: ${randomMenuId}`);

  // 3. Fetch the full menu document and populate it
  const foundMenu = await Menu.findById(randomMenuId)
    .populate({
        path: 'menuchilds.menuChildId',
        populate: [{
            path: 'ingredients.unit',
            model: 'Unit'
        }, {
            path: 'ingredients.name',
            model: 'Grocery'
        }]
    })
    .populate('users.roleId')
    .populate('users.userId');

  if (!foundMenu) {
    // This case is unlikely if aggregation found an ID, but handle it.
    // Throw an error as this indicates a potential data inconsistency.
    throw new AppError('Could not retrieve the randomly selected menu after finding its ID.', 500);
  }

  // 4. Optional: Filter menuchilds for isStandard === true
  if (foundMenu.menuchilds) {
    foundMenu.menuchilds = foundMenu.menuchilds.filter(child => child.menuChildId && child.menuChildId.isStandard === true);
  }

  // 5. Return the populated menu data
  return foundMenu;
}

// @GET /menu/random-free
exports.getRandomFreeMenu = catchAsync(async (req, res, next) => {
  helper.devConsole("[getRandomFreeMenu Route Handler] Triggered");

  const foundMenuData = await _fetchRandomFreeMenuData();

  if (!foundMenuData) {
    return next(new AppError('No free access menus found.', 404));
  }

  // Pass data to the response middleware
  req.body.answerobject = foundMenuData;
  next();
});

// Make the internal function available for other modules (like the cron job)
// NOTE: Prefixing with _ suggests it's primarily for internal use,
// but exporting allows the cron job to call it.
exports._internal_fetchRandomFreeMenuData = _fetchRandomFreeMenuData;

// 🔧 KRITISCH: Middleware um Menu-ID aus answerobject in params zu übertragen
exports.transferMenuIdToParams = catchAsync(async (req, res, next) => {
  helper.devConsole("transferMenuIdToParams - transferring menu ID from answerobject to params");

  // Hole Menu-ID aus verschiedenen möglichen Quellen
  let menuId = null;

  if (req.body.answerobject && req.body.answerobject._id) {
    menuId = req.body.answerobject._id;
  } else if (req.body.menu && req.body.menu._id) {
    menuId = req.body.menu._id;
  } else if (req.body.menu && req.body.menu.id) {
    menuId = req.body.menu.id;
  }

  if (!menuId) {
    helper.devConsole("❌ No menu ID found in request body");
    return next(new AppError('Menu ID not found in request body', 400));
  }

  // Übertrage Menu-ID in params für nachfolgende Middleware
  req.params.menuid = menuId.toString();
  helper.devConsole(`✅ Menu ID transferred to params: ${req.params.menuid}`);

  next();
});

// 🔧 KRITISCH: Reload Menu mit populated Ingredients nach AI-Generierung
// @ GET /menu/one/:menuid/populated
exports.getOneMenuPopulated = catchAsync(async (req, res, next) => {
  helper.devConsole("getOneMenuPopulated in menuController - for AI-generated recipes")

  if (!req.params.menuid) {
    return next(new AppError('Menu ID is required', 400));
  }

  helper.devConsole(`Loading menu with ID: ${req.params.menuid}`);

  // Lade Menu mit vollständig populated Ingredients
  const foundMenu = await Menu.findOne({_id: req.params.menuid})
    .populate({
        path: 'menuchilds.menuChildId',
        populate: [{
            path: 'ingredients.unit',
            model: 'Unit'
        }, {
            path: 'ingredients.name',
            model: 'Grocery'
        }]
    })
    .populate('users.roleId')
    .populate('users.userId');

  // 🔧 KRITISCH: Manuelle Population der Ingredients falls automatische Population fehlschlägt
  if (foundMenu && foundMenu.menuchilds && foundMenu.menuchilds.length > 0) {
    for (let menuchildRef of foundMenu.menuchilds) {
      if (menuchildRef.menuChildId && menuchildRef.menuChildId.ingredients) {
        for (let ingredient of menuchildRef.menuChildId.ingredients) {
          // Populate Unit falls nicht bereits populated
          if (ingredient.unit && typeof ingredient.unit === 'string') {
            try {
              const Unit = require('../models/unitModel');
              const populatedUnit = await Unit.findById(ingredient.unit);
              if (populatedUnit) {
                ingredient.unit = populatedUnit;
              }
            } catch (err) {
              helper.devConsole(`⚠️ Failed to populate unit ${ingredient.unit}: ${err.message}`);
            }
          }

          // Populate Grocery falls nicht bereits populated
          if (ingredient.name && typeof ingredient.name === 'string') {
            try {
              const Grocery = require('../models/groceryModel');
              const populatedGrocery = await Grocery.findById(ingredient.name);
              if (populatedGrocery) {
                ingredient.name = populatedGrocery;
              }
            } catch (err) {
              helper.devConsole(`⚠️ Failed to populate grocery ${ingredient.name}: ${err.message}`);
            }
          }
        }
      }
    }
  }

  if (!foundMenu) {
    return next(new AppError('Menu not found', 404));
  }

  // only export array with isStandard = true (active menuchild)
  if (foundMenu) {
    foundMenu.menuchilds = foundMenu.menuchilds.filter(child => child.menuChildId.isStandard === true);
  }

  helper.devConsole("✅ Menu loaded with populated ingredients");
  helper.devConsole(`Found ${foundMenu.menuchilds.length} standard menuchilds`);

  if (foundMenu.menuchilds.length > 0 && foundMenu.menuchilds[0].menuChildId.ingredients) {
    helper.devConsole(`First menuchild has ${foundMenu.menuchilds[0].menuChildId.ingredients.length} ingredients`);

    // Log erste 2 Ingredients für Debugging
    const firstTwoIngredients = foundMenu.menuchilds[0].menuChildId.ingredients.slice(0, 2);
    firstTwoIngredients.forEach((ing, index) => {
      helper.devConsole(`Ingredient ${index + 1}: ${ing.name?.name || 'NO NAME'} - ${ing.amount} ${ing.unit?.name || 'NO UNIT'}`);
    });
  }

  // Send success response
  req.body.answerobject = foundMenu

  // next
  next()
});

