const express = require('express');
const menuController = require('../controllers/menuController');
const menuchildController = require('../controllers/menuchildController');
const unitController = require('../controllers/unitController');
const authController = require('../controllers/authController');
const gptController = require('../controllers/gptController');
const paymentController = require('../controllers/paymentController');
const recipeToShoppingListController = require('../controllers/recipeToShoppingListController');
const AppError = require('./../utils/appError')
const multer = require('multer');

const router = express.Router();

const multerStorage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, 'public/img/recipes');
    },
    filename: (req, file, cb) => {
        const ext = file.mimetype.split('/')[1]
        cb(null, `user-${Date.now()}.${ext}`);
    }
});

const multerFilter = (req, file, cb) => {
    if(file.mimetype.startsWith('image')){
        cb(null, true)
    } else {
        cb(new AppError('Nur Bilder für den Upload erlaubt', 400), false)
    }
}

const upload = multer({
    storage: multerStorage,
    fileFilter: multerFilter
})

//////////////////////////////////////////////////////////////////////////////////////////////
// menu
router
.route('/one')
.post(authController.verify, menuController.createOneMenu, authController.sendanswer)

router
.route('/one/:menuid')
.get(authController.verify, menuController.getOneMenu, authController.sendanswer)

// check unit first and update it
.patch(authController.verify, menuController.patchOneMenu, authController.sendanswer)

// create only menu
.post(authController.verify, menuController.createOneMenu, authController.sendanswer)


router
.route('/one/:menuid/delete/relateduser')
.delete(authController.verify, menuController.deleteRelatedUserById, authController.sendanswer)


// menu free
router
.route('/one/:menuid/free')
.get(menuController.getOneFreeAccessMenu, authController.sendanswer)

//////////////////////////////////////////////////////////////////////////////////////////////

//////////////////////////////////////////////////////////////////////////////////////////////
// menuchild
router
.route('/one/child/:menuchildid')
.get(authController.verify, menuchildController.getOneMenuchild, authController.sendanswer)

// check unit first and update it
.patch(authController.verify, menuchildController.patchOneMenuchild, authController.sendanswer)

// create only menuchild
.post(authController.verify, unitController.groceryAndUnitChecker, menuchildController.createOneMenuchild, authController.sendanswer)

// check ifmenuchild exist

router
.route('/one/child/:menuchildid/createifnotexists')
.post(authController.verify, menuchildController.createAndAddOneMenuchildToMenu, menuchildController.updateRelatedMenuchildsIsStandardField, gptController.createCalculationOfNutrions, authController.sendanswer)

//////////////////////////////////////////////////////////////////////////////////////////////

//////////////////////////////////////// INGREDIENT(s) ///////////////////////////////////////
// Add One Ingredient -> check and create -> update db -> send response
router
.route('/one/:menuid/child/:menuchildid/ingredient')
.post(authController.verify, unitController.groceryAndUnitChecker, menuchildController.addOneIngredientToIngredients, authController.sendanswer)

// Remove One Ingredient by index -> preserve StableIDs -> update db -> send response
router
.route('/one/:menuid/child/:menuchildid/ingredient/:ingredientIndex')
.delete(authController.verify, menuchildController.removeOneIngredientFromIngredients, authController.sendanswer)

// Remove ingredient by stableId from ALL MenuChilds of a recipe -> global delete -> send response
router
.route('/recipe/:parentId/ingredient/:stableId/global-delete')
.delete(authController.verify, menuchildController.removeIngredientFromAllMenuChilds, authController.sendanswer)

// Add ingredient to ALL MenuChilds of a recipe with scaling -> global add -> send response
router
.route('/recipe/:parentId/ingredient/global-add')
.post(authController.verify, unitController.groceryAndUnitChecker, menuchildController.addIngredientToAllMenuChilds, authController.sendanswer)

// 🛒 EINKAUFSZETTEL-INTEGRATION: Add recipe to shopping list
router
.route('/recipe/:menuChildId/add-to-shopping-list')
.post(authController.verify, recipeToShoppingListController.addRecipeToShoppingList)

// 🛒 EINKAUFSZETTEL-INTEGRATION: Remove recipe from shopping list
router
.route('/recipe/:menuChildId/remove-from-shopping-list')
.delete(authController.verify, recipeToShoppingListController.removeRecipeFromShoppingList)

// 🔧 KRITISCH: Dedizierte PersonCount-Route
router
.route('/child/:menuchildid/person-count')
.patch(authController.verify, menuchildController.updatePersonCount, authController.sendanswer)

// 🔧 KRITISCH: Global Delete Ingredient von ALLEN MenuChilds
router
.route('/recipe/:parentId/ingredient/:stableId/global-delete')
.delete(authController.verify, menuchildController.deleteIngredientFromAllMenuChilds, authController.sendanswer)

// 🔧 KRITISCH: Populated Menu für AI-generierte Rezepte
router
.route('/one/:menuid/populated')
.get(authController.verify, menuController.getOneMenuPopulated, authController.sendanswer)

//////////////////////////////////////////////////////////////////////////////////////////////

//////////////////////////////////////////////////////////////////////////////////////////////
// complete menu
// check unit first and link it, create menu, create menuRelation between menu and user
router
.route('/complete')
.post(
    authController.verify,
    unitController.groceryAndUnitChecker,
    menuController.createOneMenu,
    menuchildController.createOneMenuchild,
    menuchildController.addOneMenuchildToMenu,
    menuController.patchOneMenu,
    // 🔧 KRITISCH: Übertrage Menu-ID und lade populated Ingredients
    menuController.transferMenuIdToParams,
    menuController.getOneMenuPopulated,
    authController.sendanswer
);

// complete menu and image
// check unit first and link it, create menu, create menuRelation between menu and user and create one image
router
.route('/completewithimage')
.post(
    authController.verify,
    unitController.groceryAndUnitChecker,
    menuController.createOneMenu,
    menuchildController.createOneMenuchild,
    menuchildController.addOneMenuchildToMenu,
    gptController.createImage,
    menuController.patchOneMenu,
    // 🔧 KRITISCH: Übertrage Menu-ID und lade populated Ingredients
    menuController.transferMenuIdToParams,
    menuController.getOneMenuPopulated,
    authController.sendanswer
);

/**
 * @openapi
 * /api/v1/menu/complete/allbyuserid/{userid}/kitchentableid/{kitchentableid}:
 *   get:
 *     tags:
 *       - Menus
 *     summary: Ruft Menüs basierend auf Benutzer und Scope ab.
 *     description: >
 *       Holt eine Liste von Menüs. Wenn der `scope`-Parameter auf `kitchentable` gesetzt ist,
 *       werden die Menüs aller Mitglieder des angegebenen Küchentisches zurückgegeben.
 *       Andernfalls (oder wenn `scope` fehlt oder auf `my_reciepts` gesetzt ist),
 *       werden nur die Menüs des Benutzers mit der angegebenen `userid` zurückgegeben.
 *       Das Ergebnis ist paginiert.
 *     parameters:
 *       - in: path
 *         name: userid
 *         required: true
 *         schema:
 *           type: string
 *           format: objectId
 *         description: Die ID des Benutzers, dessen Rezepte (oder Küchentable) abgefragt werden sollen.
 *       - in: path
 *         name: kitchentableid
 *         required: true
 *         schema:
 *           type: string
 *           format: objectId
 *         description: Die ID des Küchentisches (relevant bei scope=kitchentable).
 *       - in: query
 *         name: scope
 *         required: false
 *         schema:
 *           type: string
 *           enum: [my_reciepts, kitchentable]
 *         description: >
 *           Bestimmt den Umfang der Suche.
 *           `my_reciepts` (oder weggelassen): Nur Rezepte von `userid`.
 *           `kitchentable`: Rezepte aller Mitglieder von `kitchentableid`.
 *       - in: query
 *         name: searchstring
 *         required: false
 *         schema:
 *           type: string
 *         description: Optionaler Suchbegriff zum Filtern von Menünamen (case-insensitive).
 *       - in: query
 *         name: cookingTime
 *         required: false
 *         schema:
 *           type: integer
 *         description: Optionaler Filter für die Kochzeit (aus MenuChild).
 *       - in: query
 *         name: sort
 *         required: false
 *         schema:
 *           type: string
 *         description: Feld, nach dem sortiert werden soll (z.B. `createdAt`, `-name`).
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Die Seite der Ergebnisse für die Paginierung (0-basiert).
 *     security:
 *       - bearerAuth: [] # Annahme: JWT Bearer Token für Auth
 *     responses:
 *       '201':
 *         description: Eine Liste der gefundenen Menüs.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Menu'
 *       '400':
 *         description: Ungültige Anfrage.
 *       '401':
 *         description: Nicht autorisiert.
 *       '403':
 *         description: Zugriff verweigert.
 *       '404':
 *         description: Ressource nicht gefunden.
 *       '500':
 *         description: Interner Serverfehler.
 */
router
.route('/complete/allbyuserid/:userid/kitchentableid/:kitchentableid')
.get(authController.verify, menuController.getAllMenusByUserid, authController.sendanswer)

// all complete menus by freeAccess with searchstring (on same route)
router
.route('/complete/all/freeaccess/:searchstring')
.get(authController.verify, menuController.getAllMenusOnFreeAccess, authController.sendanswer)

// all complete menus by freeAccess without searchstring (on same route)
router
.route('/complete/all/freeaccess')
.get(authController.verify, menuController.getAllMenusOnFreeAccess, authController.sendanswer)



//////////////////////////////////////////////////////////////////////////////////////////////

//////////////////////////////////////////////////////////////////////////////////////////////
// complete menu
// /menu/menuchildconnection


//////////////////////////////////////////////////////////////////////////////////////////////

// menuRelation (get, create, update, delete)
router
.route('/related/:id')
.delete(authController.verify/*, menuRelationController.deleteOneRelation*/)
.patch(authController.verify/*, menuRelationController.patchOneRelation*/)
.post(authController.verify/*, menuRelationController.postOneRelation*/)
.get(authController.verify/*, menuRelationController.getOneRelation*/)

// zutaten, einheit oder name (grocery or unit) update checker
router
.route('/one/ingredientsrow/checker')
.post(authController.verify, unitController.einheitAndNamePreChecker)

router
.route('/images/upload')
.post(upload.single('image'), authController.verify, menuController.uploadOneImage, menuController.patchOneMenu, menuController.getOneMenu, authController.sendanswer)

router
.route('/images/:id')
.get(authController.verify, menuController.downloadOneImage)

router
.route('/related/oneuser/:id/:searchstring')
.get(authController.verify/*, menuRelationController.getAllMenusFromOneUserBySearchstring*/)

router
.route('/related/oneuser/:id')
.get(authController.verify/*, menuRelationController.getAllMenusFromOneUser*/)

router
.route('/related/severalusers/public')
.get(authController.verify/*, menuRelationController.getAllMenusWithFreeAccess*/)

router
.route('/related/severalusers/public/:searchstring')
.get(authController.verify/*, menuRelationController.getAllMenusWithFreeAccessBySearchstring*/)

router
.route('/one/byIngridient/:ingridient')
.get(menuController.getOneMenuByIngridient)

// comment it out on live
/*
router.route('/datamanipulation/menus')
.get(unitController.datamanipulationUnit)
*/

/**
 * @openapi
 * components:
 *   schemas:
 *     MenuChildReference:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           format: objectId
 *           description: Die ID des Referenzeintrags im Menü-Array.
 *         numberOfPersons:
 *           type: integer
 *           description: Die Anzahl der Personen (aus seatCount des Kindes).
 *         menuChildId:
 *           $ref: '#/components/schemas/MenuChild'
 *     MenuChild:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           format: objectId
 *         parentId:
 *           type: string
 *           format: objectId
 *         seatCount:
 *           type: integer
 *         cookingTime:
 *           type: integer
 *         isStandard:
 *           type: boolean
 *         ingredients: { type: array } # Vereinfacht, hier ggf. detaillierter
 *         preperation: { type: array, items: { type: string } }
 *         numberOfPersons: { type: integer }
 *         # ... weitere Felder ...
 *     UserReference:
 *        type: object
 *        properties:
 *          roleId: { type: string, format: objectId }
 *          userId: { type: string, format: objectId }
 *          _id: { type: string, format: objectId }
 *     Menu:
 *       type: object
 *       properties:
 *         _id: { type: string, format: objectId }
 *         name: { type: string }
 *         description: { type: string }
 *         menuchilds:
 *           type: array
 *           items: { $ref: '#/components/schemas/MenuChildReference' }
 *         users:
 *           type: array
 *           items: { $ref: '#/components/schemas/UserReference' }
 *         imagelink: { type: string }
 *         freeAccess: { type: boolean }
 *         # ... füge hier alle Felder deines Menu-Modells hinzu ...
 */

router.route('/random-free').get(authController.verify, menuController.getRandomFreeMenu, authController.sendanswer);

module.exports = router;